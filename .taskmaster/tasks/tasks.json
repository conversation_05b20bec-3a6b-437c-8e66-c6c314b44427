{"master": {"tasks": [{"id": 1, "title": "Setup Project Repository and Core Dependencies", "description": "Initialize the project repository with Next.js 15, React 19, and install all core dependencies as specified in the PRD.", "details": "Create a new Next.js 15 project with App Router and React 19 support. Install and configure Tailwind CSS, HeroUI, @heroicons/react, next-themes, framer-motion, react-intersection-observer, swr, axios, zustand, eventsource-parser, clsx, playwright, @types/*, eslint, and prettier. Set up basic project structure and linting.", "testStrategy": "Verify all dependencies are installed and project runs without errors. Check for correct versions and linting setup.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Implement Theme Toggle and Persistence", "description": "Add dark/light theme toggle with auto-detection and localStorage persistence.", "details": "Use next-themes to manage theme state. Implement a global theme toggle component. Persist theme preference in localStorage. Ensure theme is auto-detected from prefers-color-scheme. Apply dark: variants in Tailwind for all components.", "testStrategy": "Test theme toggle in UI, verify localStorage persistence, and check auto-detection on different devices.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Design and Implement Glassmorphism Navigation Bar", "description": "Create a sticky GlassNavBar with blurred backdrop for glass effect.", "details": "Design a sticky navigation bar using HeroUI components. Apply bg-white/10 dark:bg-gray-900/30, backdrop-blur-lg, and subtle border for glassmorphism effect. Ensure it is accessible and keyboard-navigable.", "testStrategy": "Verify sticky behavior, glass effect, and keyboard navigation.", "priority": "high", "dependencies": [1, 2], "status": "pending", "subtasks": []}, {"id": 4, "title": "Build Home Dashboard with Live Run Timeline", "description": "Implement the home dashboard with live run timeline via Server-Sent Events (SSE).", "details": "Create a dashboard page at / with RunTimelineCard, HealthStatBar, and StartRunButton. Use EventSource to connect to /runs/stream for live updates. Handle SSE reconnect logic with 10s back-off. Use Zustand for global state management.", "testStrategy": "Test live timeline updates, SSE reconnect, and dashboard responsiveness.", "priority": "high", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Implement Table View of Historical Runs", "description": "Add a sortable table view of historical runs with KPI columns.", "details": "Build a table component for historical runs. Include sortable columns for duration, tables, and status. Use SWR for data fetching and Zustand for state. Ensure mobile-first layout.", "testStrategy": "Test sorting, data loading, and responsive layout.", "priority": "high", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 6, "title": "Develop Run-Detail Page with Agent <PERSON> and Log Stream", "description": "Create a run-detail page with agent <PERSON><PERSON><PERSON> chart, real-time logs, and artefact download buttons.", "details": "Build /runs/[id] page with AgentGantt, LogStreamPanel, ArtefactTabs, and BlobStatusChip. Use EventSource for real-time logs. Provide download links for artefacts. Link out to DataHub.", "testStrategy": "Test Gantt chart, log streaming, download links, and DataHub integration.", "priority": "high", "dependencies": [1, 2, 3, 4], "status": "pending", "subtasks": []}, {"id": 7, "title": "Implement Artefact Inspectors (JSON/YAML/Diff)", "description": "Add artefact inspectors for JSON, YAML, and diff views.", "details": "Create /runs/[id]/artifact/[file] route. Implement JSON viewer with line numbers, YAML viewer for MetricFlow, and diff view for delta runs. Use lazy loading for large files.", "testStrategy": "Test JSON/YAML rendering, diff highlighting, and lazy loading.", "priority": "medium", "dependencies": [1, 2, 3, 6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Build Memory Search Modal with ChromaDB Integration", "description": "Implement a memory search modal with ChromaDB top-k and fuzzy search.", "details": "Create a search modal at /memory. Integrate ChromaDB for vector search. Add fuzzy search and highlighted terms. Use SearchBar, MemoryList, and MemoryDetailDrawer components.", "testStrategy": "Test search performance (<300ms), fuzzy matching, and result highlighting.", "priority": "medium", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 9, "title": "Implement Smooth Page and Component Transitions", "description": "Add smooth transitions and respect reduced motion settings.", "details": "Use Framer Motion for page and card transitions. Integrate with App-Router LayoutRouter for cross-fade. Respect prefers-reduced-motion media query.", "testStrategy": "Test transitions, reduced motion support, and cross-fade effects.", "priority": "medium", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 10, "title": "Ensure Responsive and Mobile-First Layout", "description": "Make the UI fully responsive and mobile-first.", "details": "Use Tailwind CSS grid breakpoints (sm:, md:, etc.). Show single-column cards on 375px devices and 3-column grid on desktop. Test on Chrome 123+, Safari 17+.", "testStrategy": "Test layout on mobile and desktop, verify breakpoints, and cross-browser compatibility.", "priority": "high", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 11, "title": "Implement Accessibility and Keyboard Navigation", "description": "Ensure all interactive elements are keyboard accessible and meet accessibility standards.", "details": "Make all interactive elements keyboard reachable. Test with axe-core for violations (<5). Ensure color contrast meets AA standards.", "testStrategy": "Test keyboard navigation and axe-core compliance.", "priority": "high", "dependencies": [1, 2, 3, 10], "status": "pending", "subtasks": []}, {"id": 12, "title": "Set Up Error Boundaries and Global Error Handling", "description": "Add top-level error boundaries for API and SSE failures.", "details": "Implement error boundaries in top-level layout. Surface errors in HeroUI modal. Handle API and SSE failures gracefully.", "testStrategy": "Test error handling and modal display for API/SSE failures.", "priority": "medium", "dependencies": [1, 2, 3, 4], "status": "pending", "subtasks": []}, {"id": 13, "title": "Configure PWA and SEO for Next.js", "description": "Set up Next.js PWA configuration and ensure SEO best practices.", "details": "Configure Next.js PWA for installability on desktop and mobile. Ensure Lighthouse score 90+. Add basic SEO metadata.", "testStrategy": "Test PWA install, Lighthouse audit, and SEO metadata.", "priority": "medium", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 14, "title": "Implement Automated Testing and CI Pipeline", "description": "Set up Playwright E2E, <PERSON><PERSON>, and GitHub Actions CI.", "details": "Write Playwright E2E tests for dashboard and run-detail. Add Jest + React-Testing-Library for unit tests. Configure GitHub Actions matrix (node 20/22) for lint, type-check, test, and build.", "testStrategy": "Run automated tests and verify CI pipeline.", "priority": "high", "dependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "status": "pending", "subtasks": []}, {"id": 15, "title": "Deploy and Validate Production Build", "description": "Deploy to Vercel and validate production build.", "details": "Deploy the application to Vercel. Validate all features, performance (LCP ≤ 2s, bundle < 250kB JS), accessibility, and cross-browser support.", "testStrategy": "Test all features, performance, accessibility, and browser support in production.", "priority": "high", "dependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-23T19:33:41.910Z", "updated": "2025-06-23T19:33:41.910Z", "description": "Tasks for master context"}}}