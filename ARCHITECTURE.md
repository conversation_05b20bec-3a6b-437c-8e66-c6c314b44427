# Data Catalog Agents UI - Architecture Plan

## Project Overview

A professional operations console for data-platform engineers to manage and monitor data processing agents. This application provides real-time visibility into agent runs, interactive memory exploration, and deep-linking to data catalog entities.

## System Architecture

```mermaid
graph TB
    subgraph "Frontend Application"
        UI[Next.js 15 App]
        Components[React 19 Components]
        State[Zustand Store]
        Themes[next-themes]
    end
    
    subgraph "Data Layer"
        SWR[SWR Client]
        MockAPI[Mock API Layer]
        MockSSE[Mock SSE Service]
        MockChroma[Mock ChromaDB]
        MockDataHub[Mock DataHub]
    end
    
    subgraph "Mock Data Services"
        AgentData[Agent Run Generator]
        MemoryData[Vector Search Simulator]
        CatalogData[DataHub Simulator]
        TimelineData[Live Timeline Generator]
    end
    
    UI --> State
    Components --> SWR
    SWR --> MockAPI
    MockAPI --> MockSSE
    MockAPI --> MockChroma
    MockAPI --> MockDataHub
    MockSSE --> AgentData
    MockChroma --> MemoryData
    MockDataHub --> CatalogData
    AgentData --> TimelineData
```

## Technical Stack

### Core Framework
- **Next.js 15** with App Router for full-stack development
- **React 19** for modern component architecture
- **TypeScript** for type safety and developer experience

### UI & Styling
- **Tailwind CSS** for utility-first styling
- **HeroUI** for professional component library
- **Glassmorphism** with `backdrop-blur` for modern aesthetics
- **next-themes** for dark/light mode support
- **Framer Motion** for smooth animations

### State Management & Data
- **Zustand** for global state management
- **SWR** for client-side data fetching and caching
- **Mock APIs** built into Next.js for development

### Development Tools
- **taskmaster-ai** for project and task management
- **ESLint & Prettier** for code quality
- **Husky** for git hooks

## Mock-First Development Strategy

### Core Philosophy
All external services will be simulated within the Next.js application to enable independent frontend development. This approach allows for:
- Rapid prototyping without backend dependencies
- Realistic data patterns and edge cases
- Easy transition to real services later

### Mock Service Implementations

#### 1. Mock SSE Service
```typescript
// /api/sse/agent-runs
export async function GET() {
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start(controller) {
      const interval = setInterval(() => {
        const data = generateMockAgentUpdate();
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
      }, 2000);
      
      return () => clearInterval(interval);
    }
  });
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    }
  });
}
```

#### 2. Mock ChromaDB API
```typescript
// /api/memory/search
export async function POST(request: Request) {
  const { query } = await request.json();
  const results = generateMockVectorResults(query);
  
  return Response.json({
    results: results.map(result => ({
      id: result.id,
      document: result.content,
      metadata: result.metadata,
      distance: result.similarity
    }))
  });
}
```

#### 3. Mock DataHub API
```typescript
// /api/datahub/entity/[urn]
export async function GET(request: Request, { params }: { params: { urn: string } }) {
  const entityInfo = generateMockEntityInfo(params.urn);
  
  return Response.json({
    entity: {
      urn: params.urn,
      type: entityInfo.type,
      info: entityInfo.properties,
      ownership: entityInfo.ownership,
      schema: entityInfo.schema
    }
  });
}
```

## API Abstraction Layer

### Service Interfaces
```typescript
interface AgentRunService {
  getLiveRuns(): Promise<AgentRun[]>
  getHistoricalRuns(): Promise<AgentRun[]>
  getRunDetails(id: string): Promise<RunDetails>
  subscribeToUpdates(): EventSource
}

interface VectorSearchService {
  search(query: string): Promise<SearchResult[]>
  getMemoryContext(agentId: string): Promise<MemoryContext>
}

interface DataHubService {
  getEntityInfo(urn: string): Promise<EntityInfo>
  searchEntities(query: string): Promise<EntityInfo[]>
}
```

### Implementation Strategy
```typescript
// services/agentRunService.ts
class MockAgentRunService implements AgentRunService {
  async getLiveRuns(): Promise<AgentRun[]> {
    const response = await fetch('/api/agent-runs/live');
    return response.json();
  }
  
  subscribeToUpdates(): EventSource {
    return new EventSource('/api/sse/agent-runs');
  }
}

// Easy swap for production
const agentRunService: AgentRunService = 
  process.env.NODE_ENV === 'production' 
    ? new ProductionAgentRunService()
    : new MockAgentRunService();
```

## Component Architecture

