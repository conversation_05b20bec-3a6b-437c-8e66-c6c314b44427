# Data Catalog Agents UI - Architecture Plan

## Project Overview

A professional operations console for data-platform engineers to manage and monitor data processing agents. This application provides real-time visibility into agent runs, interactive memory exploration, and deep-linking to data catalog entities.

## System Architecture

```mermaid
graph TB
    subgraph "Frontend Application"
        UI[Next.js 15 App]
        Components[React 19 Components]
        State[Zustand Store]
        Themes[next-themes]
    end
    
    subgraph "Data Layer"
        SWR[SWR Client]
        MockAPI[Mock API Layer]
        MockSSE[Mock SSE Service]
        MockChroma[Mock ChromaDB]
        MockDataHub[Mock DataHub]
    end
    
    subgraph "Mock Data Services"
        AgentData[Agent Run Generator]
        MemoryData[Vector Search Simulator]
        CatalogData[DataHub Simulator]
        TimelineData[Live Timeline Generator]
    end
    
    UI --> State
    Components --> SWR
    SWR --> MockAPI
    MockAPI --> MockSSE
    MockAPI --> MockChroma
    MockAPI --> MockDataHub
    MockSSE --> AgentData
    MockChroma --> MemoryData
    MockDataHub --> CatalogData
    AgentData --> TimelineData
```

## Technical Stack

### Core Framework

- **Next.js 15** with App Router for full-stack development
- **React 19** for modern component architecture
- **TypeScript** for type safety and developer experience

### UI & Styling

- **Tailwind CSS** for utility-first styling
- **HeroUI** for professional component library
- **Glassmorphism** with `backdrop-blur` for modern aesthetics
- **next-themes** for dark/light mode support
- **Framer Motion** for smooth animations

### State Management & Data

- **Zustand** for global state management
- **SWR** for client-side data fetching and caching
- **Mock APIs** built into Next.js for development

### Development Tools

- **taskmaster-ai** for project and task management
- **ESLint & Prettier** for code quality
- **Husky** for git hooks

## Mock-First Development Strategy

### Core Philosophy

All external services will be simulated within the Next.js application to enable independent frontend development. This approach allows for:

- Rapid prototyping without backend dependencies
- Realistic data patterns and edge cases
- Easy transition to real services later

### Mock Service Implementations

#### 1. Mock SSE Service

```typescript
// /api/sse/agent-runs
export async function GET() {
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start(controller) {
      const interval = setInterval(() => {
        const data = generateMockAgentUpdate();
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
      }, 2000);
      
      return () => clearInterval(interval);
    }
  });
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    }
  });
}
```

#### 2. Mock ChromaDB API

```typescript
// /api/memory/search
export async function POST(request: Request) {
  const { query } = await request.json();
  const results = generateMockVectorResults(query);
  
  return Response.json({
    results: results.map(result => ({
      id: result.id,
      document: result.content,
      metadata: result.metadata,
      distance: result.similarity
    }))
  });
}
```

#### 3. Mock DataHub API

```typescript
// /api/datahub/entity/[urn]
export async function GET(request: Request, { params }: { params: { urn: string } }) {
  const entityInfo = generateMockEntityInfo(params.urn);
  
  return Response.json({
    entity: {
      urn: params.urn,
      type: entityInfo.type,
      info: entityInfo.properties,
      ownership: entityInfo.ownership,
      schema: entityInfo.schema
    }
  });
}
```

## API Abstraction Layer

### Service Interfaces

```typescript
interface AgentRunService {
  getLiveRuns(): Promise<AgentRun[]>
  getHistoricalRuns(): Promise<AgentRun[]>
  getRunDetails(id: string): Promise<RunDetails>
  subscribeToUpdates(): EventSource
}

interface VectorSearchService {
  search(query: string): Promise<SearchResult[]>
  getMemoryContext(agentId: string): Promise<MemoryContext>
}

interface DataHubService {
  getEntityInfo(urn: string): Promise<EntityInfo>
  searchEntities(query: string): Promise<EntityInfo[]>
}
```

### Implementation Strategy

```typescript
// services/agentRunService.ts
class MockAgentRunService implements AgentRunService {
  async getLiveRuns(): Promise<AgentRun[]> {
    const response = await fetch('/api/agent-runs/live');
    return response.json();
  }
  
  subscribeToUpdates(): EventSource {
    return new EventSource('/api/sse/agent-runs');
  }
}

// Easy swap for production
const agentRunService: AgentRunService = 
  process.env.NODE_ENV === 'production' 
    ? new ProductionAgentRunService()
    : new MockAgentRunService();
```

## Component Architecture

### Page Structure

```
app/
├── layout.tsx                 # Root layout with providers
├── page.tsx                   # Dashboard overview
├── agents/
│   ├── page.tsx              # Agent runs list
│   └── [id]/
│       ├── page.tsx          # Agent run details
│       └── memory/
│           └── page.tsx      # Memory exploration
├── catalog/
│   └── [urn]/
│       └── page.tsx          # DataHub entity details
└── api/                      # Mock API routes
```

### Key Components

```typescript
// components/AgentRunCard.tsx
interface AgentRunCardProps {
  run: AgentRun;
  isLive?: boolean;
}

// components/MemorySearch.tsx
interface MemorySearchProps {
  agentId: string;
  onResultSelect: (result: SearchResult) => void;
}

// components/LiveTimeline.tsx
interface LiveTimelineProps {
  agentId: string;
  autoScroll?: boolean;
}
```

## Data Models

### Core Types

```typescript
interface AgentRun {
  id: string;
  agentName: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  startTime: Date;
  endTime?: Date;
  progress: number;
  currentStep?: string;
  memoryContext?: MemoryContext;
  dataEntities: string[]; // DataHub URNs
}

interface MemoryContext {
  totalVectors: number;
  recentQueries: string[];
  topConcepts: string[];
  lastUpdated: Date;
}

interface SearchResult {
  id: string;
  content: string;
  metadata: Record<string, any>;
  similarity: number;
  relatedEntities: string[];
}
```

## Implementation Roadmap

### Phase 1: Foundation (Week 1-2)

```mermaid
gantt
    title Development Timeline
    dateFormat  YYYY-MM-DD
    section Foundation
    Project Setup           :2025-01-27, 2d
    Core Layout            :2025-01-29, 2d
    Theme System           :2025-01-31, 2d
    Mock API Structure     :2025-02-02, 2d
```

**Tasks:**

- [x] Initialize Next.js project with TypeScript
- [ ] Set up Tailwind CSS and HeroUI
- [ ] Implement dark/light theme switching
- [ ] Create basic layout with navigation
- [ ] Set up Zustand store structure
- [ ] Create mock API endpoints structure

### Phase 2: Core Features (Week 3-4)

```mermaid
gantt
    title Development Timeline
    dateFormat  YYYY-MM-DD
    section Core Features
    Agent Dashboard        :2025-02-03, 3d
    Live Updates           :2025-02-06, 3d
    Agent Details          :2025-02-09, 2d
```

**Tasks:**

- [ ] Build agent runs dashboard
- [ ] Implement SSE for live updates
- [ ] Create agent run detail views
- [ ] Add real-time status indicators
- [ ] Implement data fetching with SWR

### Phase 3: Advanced Features (Week 5-6)

```mermaid
gantt
    title Development Timeline
    dateFormat  YYYY-MM-DD
    section Advanced Features
    Memory Search          :2025-02-11, 3d
    DataHub Integration    :2025-02-14, 3d
    Polish & Testing       :2025-02-17, 2d
```

**Tasks:**

- [ ] Build memory search interface
- [ ] Implement vector search results display
- [ ] Create DataHub entity pages
- [ ] Add deep-linking functionality
- [ ] Performance optimization and testing

## Mock Data Strategy

### Realistic Data Generation

```typescript
// utils/mockDataGenerators.ts
export function generateMockAgentRun(): AgentRun {
  return {
    id: nanoid(),
    agentName: faker.company.name() + ' Data Processor',
    status: faker.helpers.arrayElement(['running', 'completed', 'failed']),
    startTime: faker.date.recent({ days: 7 }),
    progress: faker.number.int({ min: 0, max: 100 }),
    currentStep: faker.helpers.arrayElement([
      'Analyzing data schema',
      'Processing records',
      'Updating catalog',
      'Generating insights'
    ]),
    dataEntities: generateDataHubUrns(3)
  };
}
```

### Dynamic Data Patterns

- **Time-based progression**: Agent runs evolve over time
- **Realistic failure scenarios**: Network timeouts, data validation errors
- **Memory search relevance**: Context-aware search results
- **Entity relationships**: Interconnected DataHub entities

## State Management

### Zustand Store Structure

```typescript
interface AppState {
  // Agent runs
  liveRuns: AgentRun[];
  historicalRuns: AgentRun[];
  selectedRun: AgentRun | null;
  
  // Memory search
  searchQuery: string;
  searchResults: SearchResult[];
  searchLoading: boolean;
  
  // UI state
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
  
  // Actions
  setSelectedRun: (run: AgentRun | null) => void;
  updateRunStatus: (id: string, status: AgentRun['status']) => void;
  performMemorySearch: (query: string) => Promise<void>;
}
```

## Performance Considerations

### Optimization Strategies

- **Virtualized lists** for large agent run collections
- **Debounced search** for memory queries
- **Memoized components** for expensive renders
- **Image optimization** with Next.js Image component
- **Bundle splitting** for code organization

### Monitoring

- **Real User Monitoring** integration ready
- **Performance budgets** defined
- **Lighthouse CI** for continuous monitoring

## Security & Best Practices

### Data Handling

- No sensitive data in mock responses
- Sanitized user inputs
- CORS properly configured
- Environment variable management

### Code Quality

- TypeScript strict mode enabled
- ESLint with React hooks plugin
- Prettier for consistent formatting
- Husky for pre-commit hooks

## Testing Strategy

### Test Coverage

- **Unit tests**: Component logic with Jest/React Testing Library
- **Integration tests**: API route testing
- **E2E tests**: Critical user flows with Playwright
- **Visual regression**: Component screenshots

### Mock Data Testing

- Realistic edge cases
- Error state handling
- Performance with large datasets
- Mobile responsiveness

## Deployment & DevOps

### Development Workflow

```mermaid
graph LR
    A[Local Development] --> B[Git Push]
    B --> C[CI/CD Pipeline]
    C --> D[Preview Deploy]
    D --> E[Production Deploy]
```

### Environment Configuration

- **Development**: Hot reload, detailed logging
- **Preview**: Production-like with test data
- **Production**: Optimized builds, monitoring

## Risk Mitigation

### Technical Risks

| Risk | Impact | Mitigation |
|------|--------|------------|
| Mock data too simple | Low realism | Use faker.js for varied, realistic data |
| Performance with large datasets | Poor UX | Implement virtualization early |
| State management complexity | Maintainability | Keep Zustand stores focused and small |
| Theme switching issues | Visual bugs | Test both themes continuously |

### Project Risks

| Risk | Impact | Mitigation |
|------|--------|------------|
| Scope creep | Timeline delay | Strict phase boundaries |
| Backend integration delays | Blocked development | Complete mock layer first |
| Design inconsistency | Poor UX | Use HeroUI design system |

## Success Metrics

### Technical Metrics

- **Performance**: <2s initial load, <100ms interactions
- **Accessibility**: WCAG 2.1 AA compliance
- **Browser support**: Modern browsers (ES2020+)
- **Mobile responsiveness**: 375px+ viewport support

### User Experience Metrics

- **Usability**: Intuitive navigation, clear information hierarchy
- **Functionality**: All core features working smoothly
- **Visual design**: Professional, modern aesthetic
- **Real-time updates**: Seamless live data streaming

## Future Integration Path

### Backend Service Integration

```typescript
// Future production service implementations
class ProductionAgentRunService implements AgentRunService {
  constructor(private apiClient: ApiClient) {}
  
  async getLiveRuns(): Promise<AgentRun[]> {
    return this.apiClient.get('/api/v1/agent-runs/live');
  }
  
  subscribeToUpdates(): EventSource {
    return new EventSource(this.apiClient.getSSEUrl('/agent-runs/stream'));
  }
}
```

### Configuration Management

```typescript
// config/services.ts
export const serviceConfig = {
  agentRuns: {
    baseUrl: process.env.AGENT_RUNS_API_URL || 'http://localhost:3000/api',
    timeout: 5000
  },
  vectorSearch: {
    baseUrl: process.env.CHROMA_API_URL || 'http://localhost:3000/api',
    collection: process.env.CHROMA_COLLECTION || 'agent-memory'
  },
  dataHub: {
    baseUrl: process.env.DATAHUB_API_URL || 'http://localhost:3000/api',
    gmsUrl: process.env.DATAHUB_GMS_URL
  }
};
```

## Conclusion

This architecture provides a solid foundation for building a professional data catalog agents UI with realistic mock backends. The mock-first approach enables independent frontend development while maintaining a clear path to production integration. The technology choices support modern development practices, excellent user experience, and future scalability.

The phased implementation approach ensures steady progress with working features at each milestone, while the comprehensive mock data strategy provides realistic testing scenarios throughout development.
