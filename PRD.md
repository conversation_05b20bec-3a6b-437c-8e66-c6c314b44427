# 1  Product Purpose

Provide a professional operations console that lets data‑platform engineers:
 • Observe live progress of multi‑agent catalog runs.
 • Inspect detailed artefacts (schema, profile, docs, quality) for any run.
 • Search vector “memories” stored in ChromaDB.
 • Validate that DataHub ingestion and Azure‑blob uploads succeeded.

⸻

## 2  Target Users

 • Data Platform Engineer – needs real‑time health & retry visibility.
 • Analytics Engineer – inspects documentation output and quality reports.
 • Data Steward – confirms profiling rules and lineage propagation.

⸻

## 3  Functional Requirements

ID Requirement Priority
UI‑1 Home dashboard streams live run timeline via Server‑Sent Events (SSE) (/runs/stream). P0
UI‑2 Table view of historical runs with sortable KPI columns (duration, tables, status). P0
UI‑3 Run‑detail page shows: agent Gantt, real‑time logs, artefact download buttons, DataHub link‑outs. P0
UI‑4 Artefact inspectors: JSON viewer with line numbers, diff view for delta runs, YAML viewer for MetricFlow. P1
UI‑5 Memory search modal (Chroma top‑k) with fuzzy search and highlighted terms. P1
UI‑6 Global dark/light theme toggle; auto‑detect prefers‑color‑scheme. P0
UI‑7 Smooth page and component transitions, respecting user “reduced motion” setting. P1
UI‑8 Works fully on Chrome (v123+) and Safari (17+). P0
UI‑9 Layout is mobile‑first; 375‑px wide devices show single‑column cards; desktop shows 3‑column grid. P0

⸻

## 4  Information Architecture & Pages

Route Purpose Key Components
/ Overview dashboard RunTimelineCard, HealthStatBar, StartRunButton
/runs/[id] Live run detail AgentGantt, LogStreamPanel, ArtefactTabs, BlobStatusChip
/runs/[id]/artifact/[file] Artefact preview JsonViewer / YamlViewer
/memory Vector search SearchBar, MemoryList, MemoryDetailDrawer
/settings Optional tokens / theme prefs ThemeToggle, TokenForm

Navigation provided by a sticky GlassNavBar using blurred backdrop (backdrop-blur-md) for glass effect.  ￼

⸻

## 5  Visual & Interaction Design
 • Component Framework – HeroUI gives pre‑styled Tailwind components; import only used modules for tree‑shaking.  ￼
 • Styling – Tailwind CSS with dark: variants and next-themes hook to persist theme.  ￼
 • Glassmorphism – apply bg-white/10 dark:bg-gray-900/30 + backdrop-blur-lg + subtle border.  ￼
 • Animations – page and card transitions with Framer Motion; tap into App‑Router LayoutRouter for cross‑fade.  ￼
 • Lazy loading – charts and JSON viewers lazy‑mount with react-intersection-observer to keep first contentful paint low.  ￼ ￼
 • Accessibility – all interactive elements keyboard reachable; motion toggles obey prefers-reduced-motion.
 • Responsive – CSS grid breakpoints sm:, md: etc. per latest Tailwind guide.  ￼

⸻

## 6  State & Data‑Flow
 • Global store – lightweight Zustand slice for auth token, theme, and run list state.
 • Real‑time updates – EventSource to /runs/{id}/events (SSE). Handle reconnect loop (10 s back‑off) to cover Vercel 15‑s idle limit.  ￼
 • Data fetching – use‑hook + App‑Router Suspense for initial page load; client swr for subsequent refetch.
 • Error boundaries – top‑level layout catches API or SSE failures and surfaces a HeroUI modal.

⸻

## 7  Non‑Functional Requirements

Area Requirement
Performance LCP ≤ 2 s on 4G; bundle < 250 kB JS after gzip (excluding vendor).
SEO / PWA Ship a Next PWA config; installable on desktop & mobile; meets Lighthouse 90+.
Browser support Tested on Chrome 123+, Safari 17+. Edge & Firefox best‑effort.  ￼
Testing Playwright E2E for dashboard and run‑detail; Jest + React‑Testing‑Library for units.
CI GitHub Actions matrix (node 20 / 22) with lint, type‑check, test, build.
Accessibility Passes axe‑core violations < 5; color‑contrast AA.

⸻

## 8  Package List & Intended Use

Package Purpose
next@15 Core React framework with App Router, React 19 support  ￼
react@19, react-dom@19 UI runtime
heroui Pre‑built Tailwind component library  ￼
tailwindcss Utility‑first styling
@heroicons/react SVG icon set matching Tailwind & HeroUI
next-themes Theme toggling (dark/light)  ￼
framer-motion Page & component animations  ￼
react-intersection-observer Lazy mounting & scroll‑triggered animations  ￼
swr Client‑side data revalidation
axios REST calls when SWR not needed
zustand Minimal global state
eventsource-parser Parsing SSE chunks
clsx Conditional class merging
playwright E2E tests
@types/*, eslint, prettier Quality gates

⸻

## 9  Milestones

Week Deliverable
1 Repo scaffold, Tailwind & HeroUI setup, dark/light toggle
2 Dashboard with live run timeline (mock SSE)
3 Run‑detail page with agent Gantt & log stream
4 Artefact viewers (JSON/YAML), download links
5 Memory search modal with Chroma endpoint
6 Responsive QA, cross‑browser tests, Lighthouse & axe‑core pass
7 Playwright E2E, CI pipeline, production build on Vercel

⸻

## 10  Acceptance Criteria

 1. Live Run – Starting a run in backend immediately surfaces on dashboard within 2 s; timeline animates smoothly.
 2. Dark/Light Modes – UI toggles instantly; persists in localStorage.
 3. Glass Visuals – All primary cards have backdrop blur and 10–20 % opacity background; verified on Safari iOS.
 4. Artefact Inspect – Selecting docs.json shows formatted JSON with copy button; diff view highlights changes between runs.
 5. Memory Search – Query “orders schema” returns snippet containing that table description in < 300 ms.
 6. Mobile – iPhone 13 Safari passes Lighthouse mobile performance 90+.
 7. Accessibility – Keyboard nav reaches every interactive element; no critical axe violations.
